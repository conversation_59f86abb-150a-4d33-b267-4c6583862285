# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    保持父类的基础功能，只添加摄像头固定中央的功能和观察空间扁平化
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境"""
        # 重置探索相关的状态
        if hasattr(self, 'last_distance'):
            delattr(self, 'last_distance')

        # 重置奖励相关的状态
        self._static_count = 0  # 重置静止计数
        if hasattr(self, '_last_speed'):
            delattr(self, '_last_speed')
        if hasattr(self, '_last_action'):
            delattr(self, '_last_action')

        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)
        obs = self._flatten_observation(obs)
        return obs, info

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        obs, reward, terminated, truncated, info = super().step(action)
        obs = self._flatten_observation(obs)
        return obs, reward, terminated, truncated, info

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def compute_reward(self, achieved_goal: np.ndarray, desired_goal: np.ndarray, info: dict, p: float = 0.5) -> float:
        """计算奖励值 - 调用父类方法"""
        return super().compute_reward(achieved_goal, desired_goal, info, p)

    def _reward(self, action: np.ndarray) -> float:
        """
        停车奖励函数（轻规则化设计）

        设计理念：
        - 给智能体目标，不给具体方法
        - 最少的人工干预，信任RL的学习能力
        - 平衡正向激励和必要约束

        奖励结构：
        1. 基础目标：到达停车位（主导学习方向）
        2. 安全约束：不撞车（硬性底线）
        3. 行为约束：不穿越其他车位（轻微约束）
        4. 路径引导：曼哈顿距离进步（轻微引导）
        5. 成功奖励：到达目标的正向激励（新增）
        6. 时间压力：防止惰性（背景压力）

        Returns:
            float: 总奖励值
        """

        # === 主导层：核心目标（-1 到 -10）===
        base_reward = self._get_base_goal_reward()

        # === 约束层：安全和行为边界 ===
        safety_constraint = self._get_safety_constraint()      # -5.0 或 0
        behavior_constraint = self._get_behavior_constraint()  # -0.1 或 0

        # === 引导层：路径提示 ===
        path_guidance = self._get_path_guidance()              # +0.15 或 0

        # === 激励层：正向奖励 ===
        success_rewards = self._get_success_rewards()          # +1.0 到 +1.5

        # === 背景层：时间压力 ===
        efficiency_pressure = self._get_efficiency_pressure()  # -0.02*progress

        # === 组合最终奖励 ===
        total_reward = (base_reward + safety_constraint + behavior_constraint +
                       path_guidance + success_rewards + efficiency_pressure)

        return total_reward

    # ===== 奖励组件实现 =====

    def _get_base_goal_reward(self) -> float:
        """
        主导层：基础目标奖励

        功能：提供主要的学习信号，告诉智能体"要到哪里去"
        来源：父类ParkingEnv的距离奖励
        范围：-1 到 -10（距离越近奖励越高）
        权重：最大，主导整个学习过程

        Returns:
            float: 基于到目标距离的奖励（负值，越接近越大）
        """
        return super()._reward(None)  # 调用父类的距离奖励

    def _get_safety_constraint(self) -> float:
        """
        约束层：安全约束

        功能：确保基本安全，这是绝对不能违反的底线
        触发：车辆发生碰撞时
        权重：很大的惩罚，硬约束

        Returns:
            float: 安全惩罚（-5.0 或 0）
        """
        return -5.0 if self.vehicle.crashed else 0.0

    def _get_behavior_constraint(self) -> float:
        """
        约束层：行为约束

        功能：防止不当行为，主要是穿越其他车位
        触发：当车辆进入非目标车位时
        权重：适中，提醒但不压制主要学习

        Returns:
            float: 行为约束惩罚（-0.1 或 0）
        """
        return self._get_simple_parking_constraint()

    def _get_path_guidance(self) -> float:
        """
        引导层：路径引导

        功能：提供路径选择的轻微提示，鼓励更直接的路径
        触发：当曼哈顿距离减少时
        权重：较小，不干扰主要目标

        Returns:
            float: 路径引导奖励（+0.15 或 0）
        """
        return self._get_manhattan_guidance()

    def _get_success_rewards(self) -> float:
        """
        激励层：成功奖励

        功能：提供正向激励，让成功停车获得正奖励
        设计：分层奖励，基础到达+摆正额外奖励
        权重：适中，提供正向激励但不压制过程学习

        Returns:
            float: 成功奖励（0 到 +1.5）
        """
        reward = 0.0
        distance = np.linalg.norm(self.vehicle.position - self.vehicle.goal.position)

        # 基础到达奖励：到达目标位置
        if distance < 1.0 and not getattr(self, '_arrived', False):
            self._arrived = True
            reward += 1.0  # 基础到达奖励

        # 摆正额外奖励：车身摆正的额外奖励
        if (getattr(self, '_arrived', False) and
            self._is_aligned() and
            not getattr(self, '_aligned', False)):
            self._aligned = True
            reward += 0.5  # 摆正额外奖励

        return reward

    def _get_efficiency_pressure(self) -> float:
        """
        背景层：效率压力

        功能：防止惰性，推动智能体及时完成任务
        计算：基于时间进度的渐进惩罚
        权重：很小，背景压力，不干扰主要学习

        Returns:
            float: 效率压力惩罚（负值，随时间增加）
        """
        return self._get_efficiency_penalty()

    # ===== 原有组件（保持不变） =====

    def _get_safety_constraints_UNUSED(self) -> float:
        """
        基本安全约束（硬约束）

        人类驾驶的安全底线：
        1. 不撞车
        2. 停车场内不超速

        Returns:
            float: 安全惩罚值
        """
        penalty = 0.0
        speed = np.linalg.norm(self.vehicle.velocity)

        # 1. 碰撞惩罚（绝对不能撞）
        if self.vehicle.crashed:
            penalty -= 5.0

        # 2. 停车场超速惩罚（人类在停车场不会开太快）
        if speed > 20.0:  # 72+ km/h，停车场内太快了
            penalty -= 1.0
        elif speed > 15.0:  # 54+ km/h，也偏快
            penalty -= 0.3

        return penalty

    def _get_wrong_spot_penalty_UNUSED(self) -> float:
        """
        误入车位惩罚（轻量化约束）

        新思路：不把其他车位当障碍物，而是当车辆误入其他车位时
        给一个小惩罚，提醒它"抓紧离开"。

        优势：
        1. 计算极简：只检查当前是否在错误车位内
        2. 行为自然：允许短暂经过，但不鼓励停留
        3. 性能优异：不需要计算距离，只需位置判断

        Returns:
            float: 误入惩罚值（小的负数或0）
        """
        current_pos = self.vehicle.position
        target_lane_index = self.vehicle.goal.lane_index

        # 检查当前是否在某个车位内
        for lane_index, lane in self.road.network.lanes_dict().items():
            if lane_index != target_lane_index:  # 不是目标车位

                # 简单的车位边界检查
                if self._is_position_in_parking_spot(current_pos, lane):
                    # 误入其他车位：给一个温和的惩罚
                    return -0.15  # 减轻惩罚，从-0.3到-0.15，减少过度保守

        return 0.0  # 在通道或目标车位内，无惩罚

    def _is_position_in_parking_spot(self, position, lane) -> bool:
        """
        判断位置是否在指定车位内

        使用简化的矩形边界检测
        """
        # 获取车位的中心和尺寸
        spot_center = lane.position(lane.length / 2, 0)

        # 简化版本：使用矩形边界（忽略旋转，适用于规整停车场）
        # 车位尺寸：长度约4米，宽度约2米
        spot_length = 4.0
        spot_width = 2.0

        # 计算相对位置
        dx = abs(position[0] - spot_center[0])
        dy = abs(position[1] - spot_center[1])

        # 简单的矩形边界检查
        return dx < spot_length / 2 and dy < spot_width / 2

    def _get_manhattan_guidance(self) -> float:
        """
        曼哈顿距离引导（路径优化提示）

        设计思路：
        - 曼哈顿距离在网格状环境中更接近实际可行路径
        - 只在距离减少时给奖励，鼓励朝目标前进
        - 不惩罚距离增加，允许必要的绕行和调头

        工作原理：
        1. 计算当前位置到目标的曼哈顿距离
        2. 与上一步的距离比较
        3. 如果减少或保持，给予小奖励
        4. 如果增加，不奖励但也不惩罚

        Returns:
            float: 曼哈顿引导奖励（+0.15 或 0）
        """
        current_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position

        # 计算曼哈顿距离（|Δx| + |Δy|）
        manhattan_dist = abs(current_pos[0] - target_pos[0]) + abs(current_pos[1] - target_pos[1])

        # 与上次距离比较
        if hasattr(self, '_last_manhattan_dist'):
            if manhattan_dist <= self._last_manhattan_dist:
                # 距离减少或保持，给予路径进步奖励
                self._last_manhattan_dist = manhattan_dist
                return 0.15  # 路径进步奖励

        # 更新距离记录
        self._last_manhattan_dist = manhattan_dist
        return 0.0  # 距离增加时不奖励，但也不惩罚

    def _get_simple_parking_constraint(self) -> float:
        """
        车位约束（防止穿越其他车位）

        设计思路：
        - 教会智能体"不要在别人的车位里停留"
        - 允许短暂穿越，但不鼓励长时间停留
        - 持续惩罚直到离开，鼓励"赶紧离开"的行为

        工作原理：
        1. 检查当前位置是否在非目标车位内
        2. 如果在错误车位内，给予持续的小惩罚
        3. 离开错误车位后，惩罚停止

        权重设计：
        - -0.1的惩罚足够提醒，但不会压制主要学习
        - 比安全约束(-5.0)小得多，优先级明确

        Returns:
            float: 车位约束惩罚（-0.1 或 0）
        """
        current_pos = self.vehicle.position
        target_lane_index = self.vehicle.goal.lane_index

        # 遍历所有车位，检查是否误入
        for lane_index, lane in self.road.network.lanes_dict().items():
            if lane_index != target_lane_index:  # 不是目标车位
                if self._is_position_in_parking_spot(current_pos, lane):
                    return -0.1  # 误入其他车位，持续小惩罚

        return 0.0  # 在通道或目标车位内，无惩罚

    def _is_position_in_parking_spot(self, position, lane) -> bool:
        """判断位置是否在指定车位内"""
        spot_center = lane.position(lane.length / 2, 0)
        spot_length, spot_width = 4.0, 2.0

        dx = abs(position[0] - spot_center[0])
        dy = abs(position[1] - spot_center[1])

        return dx < spot_length / 2 and dy < spot_width / 2

    # === 已删除的函数（简化奖励函数） ===
    # _get_smoothness_constraints - 平滑性约束
    # _get_wrong_spot_penalty - 误入车位惩罚
    # _get_simple_path_guidance - 路径引导
    # _get_basic_parking_behavior - 停车行为
    # _get_safety_constraints - 安全约束（简化为直接判断）

    def _get_smoothness_constraints_DELETED(self, action: np.ndarray) -> float:
        """
        平滑性约束（人类驾驶特征）

        人类不会：
        1. 速度忽快忽慢（抽搐式驾驶）
        2. 急加急减（除非紧急情况）
        3. 急转弯（特别是停车场内）

        Returns:
            float: 平滑性惩罚值
        """
        penalty = 0.0
        speed = np.linalg.norm(self.vehicle.velocity)

        # 1. 速度变化平滑性检查
        if hasattr(self, '_last_speed'):
            speed_change = abs(speed - self._last_speed)
            if speed_change > 4.0:  # 一步内速度变化超过4m/s（约14.4km/h）
                penalty -= 0.3  # 明显的急加速/急减速
            elif speed_change > 2.0:  # 速度变化超过2m/s
                penalty -= 0.1  # 轻微惩罚
        self._last_speed = speed

        # 2. 转向平滑性检查（如果有转向动作）
        if len(action) > 1:
            steering_action = abs(action[1])
            if steering_action > 0.8:  # 大幅转向
                if speed > 8.0:  # 高速时大幅转向很危险
                    penalty -= 0.2
                else:  # 低速时大幅转向可以接受，但轻微惩罚
                    penalty -= 0.05

        return penalty

    def _get_simple_path_guidance(self) -> float:
        """
        简化的路径引导（软引导）

        人类停车的基本路径偏好：
        1. 大致从前方接近停车位
        2. 不做复杂的绕行

        Returns:
            float: 路径引导奖励值
        """
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position

        # 简单判断：如果在目标前方，给小奖励
        if vehicle_pos[1] > target_pos[1]:  # 在目标前方
            # 如果还在合理的x范围内
            if abs(vehicle_pos[0] - target_pos[0]) < 8.0:
                return 0.2  # 适度奖励，不过分强调

        return 0.0

    def _get_basic_parking_behavior(self) -> float:
        """
        基本停车行为（软引导）

        停车的基本要求：
        1. 车身摆正（重要）
        2. 不要求特定的停车方式（删除倒车要求）

        Returns:
            float: 停车行为奖励值
        """
        bonus = 0.0
        distance = np.linalg.norm(self.vehicle.position - self.vehicle.goal.position)

        # 在接近停车位时考虑车身摆正（扩大触发范围）
        if distance < 6.0:  # 从4.0扩大到6.0，更早开始关注摆正
            # 车身摆正奖励（保留并增强）
            if self._is_aligned():
                # 距离越近，摆正奖励越大
                proximity_factor = max(0.5, (6.0 - distance) / 6.0)
                bonus += 0.25 * proximity_factor  # 动态奖励，最大0.25

        return bonus

    def _is_aligned(self) -> bool:
        """判断车身是否摆正（支持正向和反向停车）"""
        vehicle_heading = self.vehicle.heading
        target_heading = self.vehicle.goal.heading

        heading_diff = abs(vehicle_heading - target_heading)
        heading_diff = min(heading_diff, 2*np.pi - heading_diff)

        # 正向摆正（0-45度）或反向摆正（135-180度）都算摆正
        # 45度标准：大差不差就行
        return heading_diff < np.pi/4 or heading_diff > (np.pi - np.pi/4)

    def _get_hard_constraints_penalty(self, action: np.ndarray) -> float:
        """
        硬约束检查

        这些是不可妥协的安全和基本要求：
        1. 极端危险行为（必须避免）
        2. 明显错误的行为模式（必须纠正）

        Returns:
            float: 硬约束惩罚值（0或负数）
        """
        _ = action  # 保留接口，当前未使用
        penalty = 0.0
        speed = np.linalg.norm(self.vehicle.velocity)

        # 1. 极端危险速度（硬约束）
        if speed > 25.0:  # 90+ km/h，停车场内绝对不允许
            penalty -= 2.0  # 严重惩罚

        # 2. 长时间静止不动（可能卡住了）
        if hasattr(self, '_static_count'):
            if speed < 0.1:
                self._static_count += 1
            else:
                self._static_count = 0

            if self._static_count > 50:  # 静止超过50步
                penalty -= 0.5  # 鼓励移动
        else:
            self._static_count = 0

        return penalty

    def _get_soft_guidance_bonus(self) -> float:
        """
        软性引导奖励

        提供路径和方向的温和引导，但不强制：
        1. 路径偏好：鼓励从前方接近
        2. 方向引导：鼓励朝目标移动

        Returns:
            float: 软性引导奖励值
        """
        # 1. 软性路径引导（增强权重，确保有效）
        path_bonus = self._get_soft_path_bonus() * 2.0  # 适度放大

        # 2. 方向性移动引导
        direction_bonus = self._get_direction_bonus() * 1.5  # 适度放大

        return path_bonus + direction_bonus

    def _get_quality_bonus(self) -> float:
        """
        行为质量奖励

        鼓励高质量的停车行为和技巧：
        1. 停车精细化动作
        2. 平滑驾驶行为

        Returns:
            float: 质量奖励值
        """
        bonus = 0.0
        distance = np.linalg.norm(self.vehicle.position - self.vehicle.goal.position)

        # 1. 接近目标时的精细化奖励（软性）
        if distance < 5.0:  # 扩大触发范围
            # 车身摆正奖励（连续化）
            if self._is_aligned():
                alignment_quality = self._get_alignment_quality()
                bonus += 0.15 * alignment_quality  # 根据摆正程度给奖励



        # 2. 基础平滑性奖励（简化版）
        speed = np.linalg.norm(self.vehicle.velocity)
        if 0.5 < speed < 15.0:  # 合理速度范围
            bonus += 0.05

        return bonus

    def _get_alignment_quality(self) -> float:
        """
        计算车身摆正的质量（连续值）

        Returns:
            float: 摆正质量，0-1之间，1表示完全摆正
        """
        vehicle_heading = self.vehicle.heading
        target_heading = self.vehicle.goal.heading

        heading_diff = abs(vehicle_heading - target_heading)
        heading_diff = min(heading_diff, 2*np.pi - heading_diff)

        # 将角度差转换为质量分数
        max_acceptable_diff = np.pi / 3  # 60度
        quality = max(0, 1.0 - heading_diff / max_acceptable_diff)

        return quality

    def _get_behavior_bonus(self) -> float:
        """
        路径引导和停车行为奖励

        包含三个部分：
        1. 一次性前方接近奖励：鼓励从前方接近停车位
        2. 方向性引导：鼓励合理的移动方向
        3. 停车精细化奖励：鼓励正确的停车姿态（车身摆正）

        Returns:
            float: 行为引导奖励值
        """
        bonus = 0.0
        distance = np.linalg.norm(self.vehicle.position - self.vehicle.goal.position)

        # 1. 软性路径引导奖励（连续性，不强制）
        # 鼓励从合理方向接近，但不硬性要求特定路径
        bonus += self._get_soft_path_bonus()

        # 2. 方向性移动引导（持续性，适度权重）
        # 鼓励朝目标方向的合理移动
        bonus += self._get_direction_bonus()

        # 3. 停车精细化奖励（接近目标时的额外引导）
        if distance < 3.0:  # 仅在接近停车位时考虑精细化行为
            # 车身摆正奖励：鼓励正确的停车姿态（重要）
            if self._is_aligned():
                bonus += 0.3  # 增加奖励，强调车身摆正

        return bonus

    def _get_soft_path_bonus(self) -> float:
        """
        软性路径引导奖励

        使用连续函数鼓励合理的接近路径，而不是硬性要求特定区域。
        设计原则：
        - 用距离函数替代区域边界
        - 鼓励从前方接近，但不强制
        - 给智能体更多路径选择的灵活性

        Returns:
            float: 路径引导奖励值
        """
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position

        # 计算到"理想前方位置"的距离
        # 理想前方位置：目标前方3-5米的区域
        ideal_front_pos = np.array([target_pos[0], target_pos[1] + 4.0])
        distance_to_ideal = np.linalg.norm(vehicle_pos - ideal_front_pos)

        # 软性奖励：距离理想位置越近，奖励越大（但没有硬边界）
        # 最大奖励0.5，在理想位置时获得，距离10米时降为0
        path_bonus = max(0, 0.5 * (1.0 - distance_to_ideal / 10.0))

        return path_bonus

    def _get_direction_bonus(self) -> float:
        """
        方向性移动奖励

        鼓励朝目标方向的移动，使用向量点积计算方向一致性。
        比硬性的速度阈值判断更灵活。

        Returns:
            float: 方向奖励值
        """
        vehicle_pos = self.vehicle.position
        target_pos = self.vehicle.goal.position
        vehicle_vel = self.vehicle.velocity

        # 计算朝目标的方向向量
        to_goal_vector = target_pos - vehicle_pos
        goal_distance = np.linalg.norm(to_goal_vector)

        # 避免除零错误
        if goal_distance < 0.1:
            return 0.0

        # 归一化方向向量
        to_goal_unit = to_goal_vector / goal_distance

        # 计算速度方向与目标方向的一致性
        speed = np.linalg.norm(vehicle_vel)
        if speed < 0.1:  # 速度太小时不给方向奖励
            return 0.0

        vel_unit = vehicle_vel / speed
        direction_alignment = np.dot(vel_unit, to_goal_unit)

        # 方向一致性奖励：完全一致时+0.2，完全相反时-0.1
        direction_bonus = direction_alignment * 0.15

        return direction_bonus

    def _get_basic_safety(self, action: np.ndarray) -> float:
        """
        基础安全奖励

        提供最基本的安全约束，防止危险驾驶行为。
        设计原则：简单有效，不过度复杂化。

        Args:
            action: 当前动作（暂未使用）

        Returns:
            float: 安全奖励值
        """
        _ = action  # 保留参数接口，暂时不使用
        speed = np.linalg.norm(self.vehicle.velocity)
        bonus = 0.0

        # 1. 危险速度惩罚
        if speed > 20.0:  # 极速惩罚（72+ km/h），停车场内不应有此速度
            bonus -= 1.0
        elif speed > 15.0:  # 过速惩罚（54+ km/h），停车场内偏快
            bonus -= 0.3

        # 2. 合理速度鼓励
        if 1.0 < speed < 12.0:  # 合理的停车场行驶速度（3.6-43.2 km/h）
            bonus += 0.1

        return bonus

    def _get_safety_bonus(self, action: np.ndarray) -> float:
        """安全驾驶奖励：防止危险行为，特别是速度控制"""
        speed = np.linalg.norm(self.vehicle.velocity)
        steering_action = abs(action[1]) if len(action) > 1 else 0
        distance = np.linalg.norm(self.vehicle.position - self.vehicle.goal.position)
        bonus = 0.0

        # 1. 根据距离调整合理速度范围（修正：速度单位是m/s，最大可达40）
        if distance > 10.0:  # 远距离：可以稍快
            if 2.0 < speed < 15.0:  # 约7-54 km/h
                bonus += 0.05
        elif distance > 5.0:  # 中距离：中等速度
            if 1.0 < speed < 8.0:  # 约4-29 km/h
                bonus += 0.08
        else:  # 近距离：必须慢速
            if 0.1 < speed < 4.0:  # 约0.4-14 km/h
                bonus += 0.1
            # 接近目标时高速的严重惩罚
            if speed > 8.0:
                bonus -= 0.5  # 大幅增加惩罚

        # 2. 危险行为惩罚（加强）
        if speed > 25.0:  # 极速惩罚（90+ km/h）
            bonus -= 0.8
        elif speed > 18.0:  # 过快惩罚（65+ km/h）
            bonus -= 0.4

        if speed > 12.0 and steering_action > 0.5:  # 高速急转（43+ km/h）
            bonus -= 0.3

        # 3. 增强的平滑性控制
        bonus += self._get_smoothness_bonus(action, speed, steering_action)

        return bonus

    def _get_smoothness_bonus(self, action: np.ndarray, speed: float, steering_action: float) -> float:
        """平滑性奖励：鼓励平滑的驾驶行为"""
        bonus = 0.0

        # 1. 转向平滑性
        # 惩罚大幅转向，奖励小幅调整
        if steering_action < 0.2:  # 小幅转向
            bonus += 0.05
        elif steering_action > 0.7:  # 大幅转向
            bonus -= 0.15
        else:  # 中等转向
            bonus -= steering_action * 0.08

        # 2. 速度变化平滑性
        if hasattr(self, '_last_speed'):
            speed_change = abs(speed - self._last_speed)
            if speed_change < 1.0:  # 平滑速度变化
                bonus += 0.08
            elif speed_change > 5.0:  # 急剧速度变化
                bonus -= 0.3
            elif speed_change > 3.0:  # 较大速度变化
                bonus -= 0.15
        self._last_speed = speed

        # 3. 加速度平滑性
        if len(action) > 0:
            acceleration_action = abs(action[0])  # 加速度动作的绝对值
            if acceleration_action < 0.3:  # 温和加速/减速
                bonus += 0.06
            elif acceleration_action > 0.8:  # 急加速/急刹车
                bonus -= 0.2

        # 4. 动作连续性（与上一次动作的差异）
        if hasattr(self, '_last_action') and self._last_action is not None:
            if len(action) == len(self._last_action):
                action_change = np.linalg.norm(action - self._last_action)
                if action_change < 0.2:  # 动作变化小
                    bonus += 0.05
                elif action_change > 0.6:  # 动作变化大
                    bonus -= 0.1
        self._last_action = action.copy() if len(action) > 0 else None

        return bonus

    def _get_efficiency_penalty(self) -> float:
        """
        时间效率压力（防止惰性行为）

        设计思路：
        - 随着时间推移给予渐进的时间压力
        - 使用非线性函数，让后期压力更明显
        - 防止智能体在目标附近无意义徘徊

        计算方式：
        - 基于当前步数计算时间进度（0-1）
        - 使用1.5次幂，让压力渐进增加
        - 权重很小，不干扰主要学习

        效果示例（假设200步最大）：
        - 50步时：约-0.003（几乎无压力）
        - 100步时：约-0.007（轻微压力）
        - 150步时：约-0.013（适度压力）
        - 200步时：约-0.02（明显压力）

        Returns:
            float: 时间效率惩罚（负值，随时间增加）
        """
        # 获取当前步数和最大允许步数
        current_steps = self.steps
        max_steps = self.config["duration"]

        # 计算时间进度比例（0到1）
        time_progress = min(current_steps / max_steps, 1.0)

        # 渐进惩罚：使用1.5次幂让后期压力更大
        # 权重-0.02确保不会压制主要学习信号
        efficiency_penalty = -0.02 * (time_progress ** 1.5)

        return efficiency_penalty

    # ============================================================================
    # 行为判断辅助函数
    # ============================================================================







    def _is_success(self, achieved_goal: np.ndarray, desired_goal: np.ndarray) -> bool:
        """判断是否成功达到目标 - 使用更合理的成功标准"""
        # 计算原始的目标距离奖励
        base_reward = super().compute_reward(achieved_goal, desired_goal, {})

        # 使用更合理的成功标准
        return base_reward > -0.2

    def _is_terminated(self) -> bool:
        """判断回合是否结束 - 调用父类方法"""
        return super()._is_terminated()

    def _is_truncated(self) -> bool:
        """判断回合是否因为超时而终止 - 调用父类方法"""
        return super()._is_truncated()

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self):
        """
        重写渲染方法，设置固定摄像头视角
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result



def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
